using ParamManager;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using ParamManager;
using static System.Net.Mime.MediaTypeNames;
using System.Web.SessionState;

namespace DoubleCamPreliminary
{
    public class DataCache
    {
        public static string filePath;

        public static string project;
        //数据库
        public static string dbIP;
        public static string dbPort;
        public static string dbUserName;
        public static string dbPassword;
        public static string dbDataBase;

        public static bool beamSnCheck;
        public static bool diopterCheck;
        public static string station;
        public static int beamSnLength;
        public static int bracketSnLength;
        public static int oledSnLength;

        //校验数据
        public static double mtfDifferMax;
        public static double mtfDifferMin;
        public static double grayDifferMax;
        public static double grayDifferMin;
        public static double angle2DifferMax;
        public static double angle2DifferMin;
        public static double diopterDifferMax;
        public static double diopterDifferMin;
        public static double magLRDifferMin;
        public static double magLRDifferMax;
        public static double magTBDifferMin;
        public static double magTBDifferMax;

        public static double centerXDifferMin;
        public static double centerXDifferMax;
        public static double centerYDifferMin;
        public static double centerYDifferMax;
        public static double point0XDifferMin;
        public static double point0XDifferMax;
        public static double point0YDifferMin;
        public static double point0YDifferMax;
        public static double point4XDifferMin;
        public static double point4XDifferMax;
        public static double point4YDifferMin;
        public static double point4YDifferMax;
        public static double point20XDifferMin;
        public static double point20XDifferMax;
        public static double point20YDifferMin;
        public static double point20YDifferMax;
        public static double point24XDifferMin;
        public static double point24XDifferMax;
        public static double point24YDifferMin;
        public static double point24YDifferMax;

        //运行数据
        public static bool diopterDiffer;
        public static bool angle2Differ;
        public static  bool mtfDiffer;
        public static  bool grayDiffer;
        public static  string finalResult;
        public static bool magLRDiffer;
        public static bool magTBDiffer;

        public static bool centerX_Differ=false;
        public static bool centerY_Differ = false;
        public static bool point0X_Differ = false;
        public static bool point0Y_Differ = false;
        public static bool point4X_Differ = false;
        public static bool point4Y_Differ = false;
        public static bool point20X_Differ = false;
        public static bool point20Y_Differ = false;
        public static bool point24X_Differ = false;
        public static bool point24Y_Differ = false;


        public static PM pm;
        public static void Init(string filePath)
        {
            //filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "sys.ini");

            station = Read("Sys", "station", "1", filePath);

            beamSnLength = Convert.ToInt32(Read("Check", "snBeamLength", "1", filePath));
            bracketSnLength = Convert.ToInt32(Read("Check", "snBracketLength", "1", filePath));
            oledSnLength = Convert.ToInt32(Read("Check", "snOledLength", "1", filePath));

            dbIP = Read("MySql", "IP", "1", filePath);
            dbPort = Read("MySql", "Port", "1", filePath);
            dbUserName = Read("MySql", "UserName", "1", filePath);
            dbPassword = Read("MySql", "Password", "1", filePath);
            dbDataBase = Read("MySql", "DataBase", "1", filePath);

            mtfDifferMax = Convert.ToDouble(Read("Verify", "mtfDifferMax", "1", filePath));
            mtfDifferMin = Convert.ToDouble(Read("Verify", "mtfDifferMin", "1", filePath));
            grayDifferMax = Convert.ToDouble(Read("Verify", "grayDifferMax", "1", filePath));
            grayDifferMin = Convert.ToDouble(Read("Verify", "grayDifferMin", "1", filePath));
            angle2DifferMax = Convert.ToDouble(Read("Verify", "angle2DifferMax", "1", filePath));
            angle2DifferMin = Convert.ToDouble(Read("Verify", "angle2DifferMin", "1", filePath));
            diopterDifferMax = Convert.ToDouble(Read("Verify", "diopterDifferMax", "1", filePath));
            diopterDifferMin = Convert.ToDouble(Read("Verify", "diopterDifferMin", "1", filePath));
            magLRDifferMin = Convert.ToDouble(Read("Verify", "magLRDifferMin", "1", filePath));
            magLRDifferMax = Convert.ToDouble(Read("Verify", "magLRDifferMax", "1", filePath));
            magTBDifferMin = Convert.ToDouble(Read("Verify", "magTBDifferMin", "1", filePath));
            magTBDifferMax = Convert.ToDouble(Read("Verify", "magTBDifferMax", "1", filePath));

            centerXDifferMin = Convert.ToDouble(Read("Verify", "centerXDifferMin", "1", filePath));
            centerXDifferMax = Convert.ToDouble(Read("Verify", "centerXDifferMax", "1", filePath));
            centerYDifferMin = Convert.ToDouble(Read("Verify", "centerYDifferMin", "1", filePath));
            centerYDifferMax = Convert.ToDouble(Read("Verify", "centerYDifferMax", "1", filePath));
            point0XDifferMin = Convert.ToDouble(Read("Verify", "point0XDifferMin", "1", filePath));
            point0XDifferMax = Convert.ToDouble(Read("Verify", "point0XDifferMax", "1", filePath));
            point0YDifferMin = Convert.ToDouble(Read("Verify", "point0YDifferMin", "1", filePath));
            point0YDifferMax = Convert.ToDouble(Read("Verify", "point0YDifferMax", "1", filePath));
            point4XDifferMin = Convert.ToDouble(Read("Verify", "point4XDifferMin", "1", filePath));
            point4XDifferMax = Convert.ToDouble(Read("Verify", "point4XDifferMax", "1", filePath));
            point4YDifferMin = Convert.ToDouble(Read("Verify", "point4YDifferMin", "1", filePath));
            point4YDifferMax = Convert.ToDouble(Read("Verify", "point4YDifferMax", "1", filePath));
            point20XDifferMin = Convert.ToDouble(Read("Verify", "point20XDifferMin", "1", filePath));
            point20XDifferMax = Convert.ToDouble(Read("Verify", "point20XDifferMax", "1", filePath));
            point20YDifferMin = Convert.ToDouble(Read("Verify", "point20YDifferMin", "1", filePath));
            point20YDifferMax = Convert.ToDouble(Read("Verify", "point20YDifferMax", "1", filePath));
            point24XDifferMin = Convert.ToDouble(Read("Verify", "point24XDifferMin", "1", filePath));
            point24XDifferMax = Convert.ToDouble(Read("Verify", "point24XDifferMax", "1", filePath));
            point24YDifferMin = Convert.ToDouble(Read("Verify", "point24YDifferMin", "1", filePath));
            point24YDifferMax = Convert.ToDouble(Read("Verify", "point24YDifferMax", "1", filePath));


            beamSnCheck = Convert.ToBoolean(Read("Verify", "beamSnCheck", "false", filePath));
            diopterCheck = Convert.ToBoolean(Read("Verify", "diopterCheck", "false", filePath));
        }

        public static void InitNew(bool flag)
        {
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;

            pm.InitAll(flag, 26, 0);
            pm.readPosition = 0;

            Para_get(ref pm);

        }

        public static void openSetting()
        {
            pm.ShowForm("SingleStation", "");
        }

        public static int Init_Local()
        {
            pm = new PM();
            pm.updateStationData += UpdateStationDataFunc;
            int res = pm.ReadConfigFile();
            if (res != 0)
                return res;

            Para_get(ref pm);

            return 0;
        }

        private static void UpdateStationDataFunc(string stationNo, string projectName, string mode)
        {
            project = projectName;
        }


        public static void Para_get(ref PM pm)
        {
            beamSnLength = pm.Vi("Check#beamSnLength");
            bracketSnLength = pm.Vi("Check#snBracketLength");
            oledSnLength = pm.Vi("Check#snOledLength");
            mtfDifferMax = pm.Vd("Verify#mtfDifferMax");
            mtfDifferMin = pm.Vd("Verify#mtfDifferMin");
            grayDifferMax = pm.Vd("Verify#grayDifferMax");
            grayDifferMin = pm.Vd("Verify#grayDifferMin");
            angle2DifferMax = pm.Vd("Verify#angle2DifferMax");
            angle2DifferMin = pm.Vd("Verify#angle2DifferMin");
            diopterDifferMax = pm.Vd("Verify#diopterDifferMax");
            diopterDifferMin = pm.Vd("Verify#diopterDifferMin");
            magLRDifferMin = pm.Vd("Verify#magLRDifferMin");
            magLRDifferMax = pm.Vd("Verify#magLRDifferMax");
            magTBDifferMin = pm.Vd("Verify#magTBDifferMin");
            magTBDifferMax = pm.Vd("Verify#magTBDifferMax");

            centerXDifferMin = pm.Vd("Verify#centerXDifferMin");
            centerXDifferMax = pm.Vd("Verify#centerXDifferMax");
            centerYDifferMin = pm.Vd("Verify#centerYDifferMin");
            centerYDifferMax = pm.Vd("Verify#centerYDifferMax");
            point0XDifferMin = pm.Vd("Verify#point0XDifferMin");
            point0XDifferMax = pm.Vd("Verify#point0XDifferMax");
            point0YDifferMin = pm.Vd("Verify#point0YDifferMin");
            point0YDifferMax = pm.Vd("Verify#point0YDifferMax");
            point4XDifferMin = pm.Vd("Verify#point4XDifferMin");
            point4XDifferMax = pm.Vd("Verify#point4XDifferMax");
            point4YDifferMin = pm.Vd("Verify#point4YDifferMin");
            point4YDifferMax = pm.Vd("Verify#point4YDifferMax");
            point20XDifferMin = pm.Vd("Verify#point20XDifferMin");
            point20XDifferMax = pm.Vd("Verify#point20XDifferMax");
            point20YDifferMin = pm.Vd("Verify#point20YDifferMin");
            point20YDifferMax = pm.Vd("Verify#point20YDifferMax");
            point24XDifferMin = pm.Vd("Verify#point24XDifferMin");
            point24XDifferMax = pm.Vd("Verify#point24XDifferMax");
            point24YDifferMin = pm.Vd("Verify#point24YDifferMin");
            point24YDifferMax = pm.Vd("Verify#point24YDifferMax");

            beamSnCheck = pm.Vb("Check#beamSnCheck");
            diopterCheck = pm.Vb("Check#diopterCheck");

            Logs.WriteDebug("param Check#beamSnLength " + beamSnLength.ToString(), true);
            Logs.WriteDebug("param Check#snBracketLength " + bracketSnLength.ToString(), true);
            Logs.WriteDebug("param Check#snOledLength " + oledSnLength.ToString(), true);
            Logs.WriteDebug("param Verify#mtfDifferMax " + mtfDifferMax.ToString(), true);
            Logs.WriteDebug("param Verify#mtfDifferMin " + mtfDifferMin.ToString(), true);
            Logs.WriteDebug("param Verify#grayDifferMax " + grayDifferMax.ToString(), true);
            Logs.WriteDebug("param Verify#grayDifferMin " + grayDifferMin.ToString(), true);
            Logs.WriteDebug("param Verify#angle2DifferMax " + angle2DifferMax.ToString(), true);
            Logs.WriteDebug("param Verify#angle2DifferMin " + angle2DifferMin.ToString(), true);
            Logs.WriteDebug("param Verify#diopterDifferMax " + diopterDifferMax.ToString(), true);
            Logs.WriteDebug("param Verify#diopterDifferMin " + diopterDifferMin.ToString(), true);
            Logs.WriteDebug("param Check#beamSnCheck " + beamSnCheck.ToString(), true);
            Logs.WriteDebug("param Check#diopterCheck " + diopterCheck.ToString(), true);
        }


        [DllImport("kernel32")]
        public static extern int GetPrivateProfileString(string lpAppName, string lpKeyName, string lpDefault, StringBuilder lpReturnedString, int nSize, string lpFileName);

        [DllImport("kernel32")]
        public static extern int WritePrivateProfileString(string lpApplicationName, string lpKeyName, string lpString, string lpFileName);

        public static string Read(string section, string key, string def, string filePath)
        {
            StringBuilder sb = new StringBuilder(1024);
            GetPrivateProfileString(section, key, def, sb, 1024, filePath);
            return sb.ToString();
        }

        public static int Write(string section, string key, string value, string filePath)
        {
            return WritePrivateProfileString(section, key, value, filePath);
        }

    }
}
