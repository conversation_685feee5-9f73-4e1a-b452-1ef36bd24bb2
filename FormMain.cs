using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using ParamManager;
using log4net.Util;
using System.Diagnostics;
using System.Text.Json;
using System.Runtime.CompilerServices;

namespace DoubleCamPreliminary
{
    public partial class FormMain : Form
    {
        public FormMain()
        {
            InitializeComponent();
        }

        static bool auto_oledsn = false;
        bool isExited = false;
        string snRemote;  // 扫码程序传过来的sn码
        byte[] resultBuf = new byte[128];
        int resultLen = 128;
        public PM pm;

        private void FormMain_Load(object sender, EventArgs e)
        {
            Logs log = new Logs();
            // 获取当前日期
            DateTime now = DateTime.Now;
            string version = now.ToString("yyyyMMdd");

            //DataCache.Init();

            //新框架
            int ret = 0;

            ret = system_init();
            if (ret != 0)
            {
                MessageBox.Show(this, "程序初始化失败");
                Application.Exit();
            }

            this.Text = "双目预检 " + System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString()
                + " 线号：" + LOGINFO.lname + " 项目:" + LOGINFO.pname;

            //初始化不良项
            optCode = OpenCSV("OptCode.csv");
            optCode = optCode.Select("工站 = '单目'").CopyToDataTable();
            HashSet<string> set = new HashSet<string>();
            set.Add("无");
            foreach (DataRow dr in optCode.Rows)
            {
                string location = dr[1].ToString();
                set.Add(location);
            }
            CB_Position_1.DataSource = set.ToList();
            CB_Position_2.DataSource = set.ToList();
            CB_Position_3.DataSource = set.ToList();
            CB_Reject_1.DisplayMember = "不良项";
            CB_Reject_1.ValueMember = "不良代码";
            CB_Reject_2.DisplayMember = "不良项";
            CB_Reject_2.ValueMember = "不良代码";
            CB_Reject_3.DisplayMember = "不良项";
            CB_Reject_3.ValueMember = "不良代码";

            CleanAll();
            //TB_BeamSn.Focus();

            // Add three rows
            dataGridView1.RowHeadersWidth = 100;
            dataGridView1.Rows.Add(3);
            dataGridView1.Rows[0].HeaderCell.Value = "左目";
            dataGridView1.Rows[1].HeaderCell.Value = "右目";
            dataGridView1.Rows[2].HeaderCell.Value = "Diff";
        }

        private int system_init()
        {
            int ret = XrPLCom.xrCInit();
            if (ret != 0)
            {
                MessageBox.Show(this, "无法连接扫码程序 ");
                return -1;
            }

            int serverEnable = 0;
            int mesEnable = 0;

            if (LOGINFO.mode == "online")
            {
                serverEnable = 1;
            }
            else
                if (LOGINFO.mode == "offline")
            {
                if (LOGINFO.configMode == "net" || LOGINFO.dbWrite == "true")
                    serverEnable = 1;
                else
                    serverEnable = 0;
            }

            if (LOGINFO.mesEnable == "true")
                mesEnable = 1;
            else
                mesEnable = 0;

            if (serverEnable == 1 || mesEnable == 1)
                ret = XrPLCom.xrCommInit(serverEnable, mesEnable, Encoding.UTF8.GetBytes(LOGINFO.user));

            if (ret != 0)
            {
                MessageBox.Show(this, "请检查网络 ret " + ret);
                return -1;
            }

            Logs.WriteDebug("LOGINFO mode " + LOGINFO.mode.ToString(), true);
            Logs.WriteDebug("LOGINFO user " + LOGINFO.user.ToString(), true);
            Logs.WriteDebug("LOGINFO rname " + LOGINFO.rname.ToString(), true);
            Logs.WriteDebug("LOGINFO lname " + LOGINFO.lname.ToString(), true);
            Logs.WriteDebug("LOGINFO pname " + LOGINFO.pname.ToString(), true);
            Logs.WriteDebug("LOGINFO configMode " + LOGINFO.configMode.ToString(), true);
            Logs.WriteDebug("LOGINFO dbWrite " + LOGINFO.dbWrite.ToString(), true);
            Logs.WriteDebug("LOGINFO mesEnable " + LOGINFO.mesEnable.ToString(), true);
            Logs.WriteDebug("LOGINFO pass " + LOGINFO.pass.ToString(), true);

            if (LOGINFO.mode == "online" ||
                (LOGINFO.mode == "offline" && LOGINFO.configMode == "net"))
                DataCache.InitNew(true);
            else
            {
                ret = DataCache.Init_Local();
                if (ret != 0)
                {
                    MessageBox.Show("请检查本地配置文件 sys.ini ");
                    Process.GetCurrentProcess().Kill();
                }
            }

            if (LOGINFO.scan == "true")
            {
                Thread thread = new Thread(new ThreadStart(test_Execute));
                thread.Start();
            }

            return 0;
        }

        void test_Execute()
        {
            byte[] snInfo = new byte[1024];
            byte[] resultBuf = new byte[128];
            while (!isExited)
            {
                int len = 1024;
                int resultLen = 128;
                int ret = XrPLCom.SnWaitNotify(snInfo, ref len);
                this.Invoke((EventHandler)delegate
                {
                    this.WindowState = FormWindowState.Normal;
                    this.TopMost = true;
                });
                
                if (ret != 0)
                {
                    Logs.WriteError("SnWaitNotify error, ret " + ret, true);
                    MessageBox.Show(this, "请重新扫码或联系管理员");
                    XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
                    this.WindowState = FormWindowState.Minimized;
                    continue;
                }

                string jsonString = Encoding.Default.GetString(snInfo, 0, len);
                Context ctx = JsonSerializer.Deserialize<Context>(jsonString);
                if (ctx == null)
                {
                    Logs.WriteError("json字符串错误 " + jsonString, true);
                    MessageBox.Show(this, "请重新扫码或联系管理员");
                    XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
                    this.WindowState = FormWindowState.Minimized;
                    continue;
                }
                snRemote = ctx.SN;
                Logs.WriteDebug("sn " + snRemote, true);
                Logs.WriteDebug("横梁SN码长度 " + snRemote.Length + " 配置长度 " + DataCache.beamSnLength, true);
                if (snRemote.Length != DataCache.beamSnLength)
                {
                    this.Invoke(new Action(() => {
                        Label_Tip.Text = "横梁SN码长度不符！";
                        Label_Tip.Visible = true;
                        beam_sn_label.Text = "";
                    }));
                    
                    MessageBox.Show(this, "横梁SN码长度不符,请重新扫码");
                    Logs.WriteDebug("横梁SN码长度不符 现在长度" + snRemote.Length + " 配置长度 " + DataCache.beamSnLength, true);
                    XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
                    continue;
                }
                
                this.Invoke(new Action(() => {
                    Logs.WriteDebug("切换 ", true);
                    this.WindowState = FormWindowState.Minimized;
                    this.Show();
                    this.WindowState = FormWindowState.Normal;

                    //this.Activate();
                    //this.TopMost = true;
                    //this.BringToFront();
                    //this.Focus();
                    TB_LeftBracketSn.Focus();
                    TB_LeftBracketSn.SelectAll();
                    beam_sn_label.Text = snRemote;
                    Label_Tip.Visible = false;
                    this.TopMost = false;
                }));                                               
            }
        }

        private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
        {

        }

        #region 关键方法
        private void CleanAll()
        {
            Label_Tip.Visible = false;

            beam_sn_label.Text = "";
            TB_LeftBracketSn.Text = "";
            TB_LeftOledSn.Text = "";
            TB_RightBracketSn.Text = "";
            TB_RightOledSn.Text = "";
            TB_FixSN.Text = "";
            tb_FixBeam.Text = "";

            // Reset all grid cells
            foreach (DataGridViewRow row in dataGridView1.Rows)
            {
                foreach (DataGridViewCell cell in row.Cells)
                {
                    cell.Value = "";
                    cell.Style.BackColor = Color.White;
                }
            }


            CB_Position_1.SelectedIndex = 0;
            CB_Position_2.SelectedIndex = 0;
            CB_Position_3.SelectedIndex = 0;
            TB_Remark.Text = "";

            Btn_OK.Visible = false;
            Btn_NG.Visible = false;
            DataCache.finalResult = "False";           
        }

        private void ConfigItem_Click(object sender, EventArgs e)
        {
            DataCache.openSetting();
        }

        private void UpdateGridValues(string parameter, double leftValue, double rightValue, double min, double max)
        {
            int colIndex = dataGridView1.Columns[parameter].Index;

            // Update left and right values
            dataGridView1.Rows[0].Cells[colIndex].Value = leftValue.ToString("F4");
            dataGridView1.Rows[1].Cells[colIndex].Value = rightValue.ToString("F4");

            // Calculate and update difference
            double diff = leftValue - rightValue;
            dataGridView1.Rows[2].Cells[colIndex].Value = diff.ToString("F4");

            // Color the diff cell based on threshold
            if (diff >= min && diff <= max)
            {
                dataGridView1.Rows[2].Cells[colIndex].Style.BackColor = Color.LightGreen;
            }
            else
            {
                dataGridView1.Rows[2].Cells[colIndex].Style.BackColor = Color.PaleVioletRed;
            }
        }


        private void CheckTestValue()
        {
            try
            {
                // Check MTF values
                string leftMtfStr = dataGridView1.Rows[0].Cells["MTF"].Value?.ToString();
                string rightMtfStr = dataGridView1.Rows[1].Cells["MTF"].Value?.ToString();

                if (!string.IsNullOrEmpty(leftMtfStr) && !string.IsNullOrEmpty(rightMtfStr) &&
                    leftMtfStr != "" && rightMtfStr != "")
                {
                    if (double.TryParse(leftMtfStr, out double leftMtf) &&
                        double.TryParse(rightMtfStr, out double rightMtf))
                    {
                        double mtf = Math.Abs(leftMtf - rightMtf);
                        dataGridView1.Rows[2].Cells["MTF"].Value = mtf.ToString("F4");

                        if (mtf >= DataCache.mtfDifferMin && mtf <= DataCache.mtfDifferMax)
                        {
                            DataCache.mtfDiffer = true;
                            dataGridView1.Rows[2].Cells["MTF"].Style.BackColor = Color.LightGreen;
                        }
                        else
                        {
                            DataCache.mtfDiffer = false;
                            dataGridView1.Rows[2].Cells["MTF"].Style.BackColor = Color.PaleVioletRed;
                        }
                    }
                }
                else
                {
                    DataCache.mtfDiffer = false;
                    dataGridView1.Rows[2].Cells["MTF"].Value = "";
                    dataGridView1.Rows[2].Cells["MTF"].Style.BackColor = Color.LightGray;
                }

                // Check Gray values
                string leftGrayStr = dataGridView1.Rows[0].Cells["Gray"].Value?.ToString();
                string rightGrayStr = dataGridView1.Rows[1].Cells["Gray"].Value?.ToString();

                if (!string.IsNullOrEmpty(leftGrayStr) && !string.IsNullOrEmpty(rightGrayStr) &&
                    leftGrayStr != "" && rightGrayStr != "")
                {
                    if (double.TryParse(leftGrayStr, out double leftGray) &&
                        double.TryParse(rightGrayStr, out double rightGray))
                    {
                        double gray = Math.Abs(leftGray - rightGray);
                        dataGridView1.Rows[2].Cells["Gray"].Value = gray.ToString("F4");

                        if (gray >= DataCache.grayDifferMin && gray <= DataCache.grayDifferMax)
                        {
                            DataCache.grayDiffer = true;
                            dataGridView1.Rows[2].Cells["Gray"].Style.BackColor = Color.LightGreen;
                        }
                        else
                        {
                            DataCache.grayDiffer = false;
                            dataGridView1.Rows[2].Cells["Gray"].Style.BackColor = Color.PaleVioletRed;
                        }
                    }
                }
                else
                {
                    DataCache.grayDiffer = false;
                    dataGridView1.Rows[2].Cells["Gray"].Value = "";
                    dataGridView1.Rows[2].Cells["Gray"].Style.BackColor = Color.LightGray;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("CheckTestValue error: " + ex.Message);
                // Reset difference values to safe state
                DataCache.mtfDiffer = false;
                DataCache.grayDiffer = false;
            }
        }

        private void CheckResult()
        {
            if(beam_sn_label.Text == "" ||
                TB_LeftBracketSn.Text == "" ||
                TB_LeftOledSn.Text == "" ||
                TB_RightBracketSn.Text == "" ||
                TB_RightOledSn.Text == "")
            {
                Btn_OK.Visible = false;
                Btn_NG.Visible = false;
                //beam_sn_label.Text = "";
                //MessageBox.Show(this, "SN为空");
                //XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
            }

            
            if (beam_sn_label.Text == TB_LeftBracketSn.Text || beam_sn_label.Text== TB_RightBracketSn.Text)
            {
                if (TB_LeftBracketSn.Text!="" || TB_RightBracketSn.Text!="")
                {
                    Label_Tip.Text = "请检查横梁SN或套筒SN是否正确！";
                    Label_Tip.Visible = true;
                    //TB_BeamSn.Focus();
                    beam_sn_label.Text = "";
                    MessageBox.Show(this, "请检查横梁SN或套筒SN是否正确");
                    XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
                }
            }
            else
            {
                DataCache.diopterDiffer = true;
                CheckAngle2();
                CheckMag();
                CheckPoint();
                if (DataCache.diopterCheck)
                {
                    CheckDiopter();
                }
                if (DataCache.grayDiffer && DataCache.mtfDiffer && string.IsNullOrEmpty(TB_Code_1.Text)
                    && DataCache.angle2Differ && DataCache.diopterDiffer && DataCache.magLRDiffer && DataCache.magTBDiffer
                    && string.IsNullOrEmpty(TB_Code_2.Text) && string.IsNullOrEmpty(TB_Code_3.Text)
                    && DataCache.centerX_Differ && DataCache.centerY_Differ && DataCache.point0X_Differ && DataCache.point0Y_Differ
                    && DataCache.point4X_Differ && DataCache.point4Y_Differ && DataCache.point20X_Differ && DataCache.point20Y_Differ
                    && DataCache.point24X_Differ && DataCache.point24Y_Differ)
                {
                    if (beam_sn_label.Text == "" ||
                        TB_LeftBracketSn.Text == "" ||
                        TB_LeftOledSn.Text == "" ||
                        TB_RightBracketSn.Text == "" ||
                        TB_RightOledSn.Text == "")
                    {
                        Logs.WriteDebug("5码未全部扫好", true);
                        return;
                    }

                    DataCache.finalResult = "True";
                    //Btn_OK.Visible = true;
                    Logs.WriteDebug("自动上传正确数据",true);
                    sqlExcute();
                    Btn_NG.Visible = false;
                    Logs.WriteDebug("上传结束", true);
                    //TB_BeamSn.Focus();
                    
                    //Thread.Sleep(1000);
                    if (this.radioButton2.Checked)
                    {
                        CleanAll();
                        TB_FixSN.Text = "";
                        TB_LeftOledSn.BackColor = Color.White;
                        TB_RightOledSn.BackColor = Color.White;
                        TB_FixSN.Focus();
                    }
                    beam_sn_label.Text = "";
                    //MessageBox.Show(this, "上传结束");
                    XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
                }
                else
                {
                    DataCache.finalResult = "False";
                    Btn_OK.Visible = false;
                    Btn_NG.Visible = true;
                }            
            }
        }

        private void CheckMag()
        {
            if (TB_LeftBracketSn.Text == "" || TB_RightBracketSn.Text == "")
            {
                return;
            }

            // Get mag rates for left bracket in one query
            double Left_magLR, Left_magTB;
            int leftRes = MySqlController.GetMagRates(TB_LeftBracketSn.Text, out Left_magLR, out Left_magTB);

            // Get mag rates for right bracket in one query
            double Right_magLR, Right_magTB;
            int rightRes = MySqlController.GetMagRates(TB_RightBracketSn.Text, out Right_magLR, out Right_magTB);

            // Initialize mag differences to false if queries failed
            if (leftRes != 0 || rightRes != 0)
            {
                DataCache.magLRDiffer = false;
                DataCache.magTBDiffer = false;
                return;
            }

            // Update MagLR values in grid and check difference
            UpdateGridValues("lr_magrate", Left_magLR, Right_magLR, DataCache.magLRDifferMin, DataCache.magLRDifferMax);

            // Calculate difference and update status
            double differenceLR = Left_magLR - Right_magLR;
            if (differenceLR > DataCache.magLRDifferMin && differenceLR < DataCache.magLRDifferMax)
            {
                Logs.WriteDebug("左右目magLR差:" + differenceLR.ToString(), true);
                DataCache.magLRDiffer = true;
            }
            else
            {
                Logs.WriteDebug("左右目magLR差异常:" + differenceLR.ToString(), true);
                DataCache.magLRDiffer = false;
            }

            // Update MagTB values in grid and check difference
            UpdateGridValues("tb_magrate", Left_magTB, Right_magTB, DataCache.magTBDifferMin, DataCache.magTBDifferMax);

            // Calculate difference and update status
            double differenceTB = Left_magTB - Right_magTB;
            if (differenceTB > DataCache.magTBDifferMin && differenceTB < DataCache.magTBDifferMax)
            {
                Logs.WriteDebug("左右目magTB差:" + differenceTB.ToString(), true);
                DataCache.magTBDiffer = true;
            }
            else
            {
                Logs.WriteDebug("左右目magTB差异常:" + differenceTB.ToString(), true);
                DataCache.magTBDiffer = false;
            }
        }

        private void CheckPoint()
        {
            if (TB_LeftBracketSn.Text == "" || TB_RightBracketSn.Text == "")
            {
                return;
            }

            // Get all points for left bracket in one query
            Dictionary<string, double> leftPoints;
            int leftRes = MySqlController.GetPoints(TB_LeftBracketSn.Text, out leftPoints);

            // Get all points for right bracket in one query
            Dictionary<string, double> rightPoints;
            int rightRes = MySqlController.GetPoints(TB_RightBracketSn.Text, out rightPoints);

            // Initialize all point differences to false if queries failed
            if (leftRes != 0 || rightRes != 0)
            {
                DataCache.centerX_Differ = false;
                DataCache.centerY_Differ = false;
                DataCache.point0X_Differ = false;
                DataCache.point0Y_Differ = false;
                DataCache.point4X_Differ = false;
                DataCache.point4Y_Differ = false;
                DataCache.point20X_Differ = false;
                DataCache.point20Y_Differ = false;
                DataCache.point24X_Differ = false;
                DataCache.point24Y_Differ = false;
                return;
            }

            // Check Center X
            double Left_centerX = leftPoints["centerX"];
            double Right_centerX = rightPoints["centerX"];
            UpdateGridValues("Center_X", Left_centerX, Right_centerX, DataCache.centerXDifferMin, DataCache.centerXDifferMax);
            double differenceCenterX = Left_centerX - Right_centerX;
            if (differenceCenterX > DataCache.centerXDifferMin && differenceCenterX < DataCache.centerXDifferMax)
            {
                Logs.WriteDebug("左右目centerX差:" + differenceCenterX.ToString(), true);
                DataCache.centerX_Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目centerX差异常:" + differenceCenterX.ToString(), true);
                DataCache.centerX_Differ = false;
            }

            // Check Center Y
            double Left_centerY = leftPoints["centerY"];
            double Right_centerY = rightPoints["centerY"];
            UpdateGridValues("Center_Y", Left_centerY, Right_centerY, DataCache.centerYDifferMin, DataCache.centerYDifferMax);
            double differenceCenterY = Left_centerY - Right_centerY;
            if (differenceCenterY > DataCache.centerYDifferMin && differenceCenterY < DataCache.centerYDifferMax)
            {
                Logs.WriteDebug("左右目centerY差:" + differenceCenterY.ToString(), true);
                DataCache.centerY_Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目centerY差异常:" + differenceCenterY.ToString(), true);
                DataCache.centerY_Differ = false;
            }

            // Check Point0 X
            double Left_point0X = leftPoints["point0X"];
            double Right_point0X = rightPoints["point0X"];
            UpdateGridValues("Point0_X", Left_point0X, Right_point0X, DataCache.point0XDifferMin, DataCache.point0XDifferMax);
            double differencePoint0X = Left_point0X - Right_point0X;
            if (differencePoint0X > DataCache.point0XDifferMin && differencePoint0X < DataCache.point0XDifferMax)
            {
                Logs.WriteDebug("左右目point0X差:" + differencePoint0X.ToString(), true);
                DataCache.point0X_Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目point0X差异常:" + differencePoint0X.ToString(), true);
                DataCache.point0X_Differ = false;
            }

            // Check Point0 Y
            double Left_point0Y = leftPoints["point0Y"];
            double Right_point0Y = rightPoints["point0Y"];

            UpdateGridValues("Point0_Y", Left_point0Y, Right_point0Y, DataCache.point0YDifferMin, DataCache.point0YDifferMax);
            double differencePoint0Y = Left_point0Y - Right_point0Y;
            if (differencePoint0Y > DataCache.point0YDifferMin && differencePoint0Y < DataCache.point0YDifferMax)
            {
                Logs.WriteDebug("左右目point0Y差:" + differencePoint0Y.ToString(), true);
                DataCache.point0Y_Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目point0Y差异常:" + differencePoint0Y.ToString(), true);
                DataCache.point0Y_Differ = false;
            }

            // Check Point4 X
            double Left_point4X = leftPoints["point4X"];
            double Right_point4X = rightPoints["point4X"];
            UpdateGridValues("Point4_X", Left_point4X, Right_point4X, DataCache.point4XDifferMin, DataCache.point4XDifferMax);
            double differencePoint4X = Left_point4X - Right_point4X;
            DataCache.point4X_Differ = differencePoint4X > DataCache.point4XDifferMin && differencePoint4X < DataCache.point4XDifferMax;
            Logs.WriteDebug("左右目point4X差:" + (DataCache.point4X_Differ ? "" : "异常:") + differencePoint4X.ToString(), true);

            // Check Point4 Y
            double Left_point4Y = leftPoints["point4Y"];
            double Right_point4Y = rightPoints["point4Y"];
            UpdateGridValues("Point4_Y", Left_point4Y, Right_point4Y, DataCache.point4YDifferMin, DataCache.point4YDifferMax);
            double differencePoint4Y = Left_point4Y - Right_point4Y;
            DataCache.point4Y_Differ = differencePoint4Y > DataCache.point4YDifferMin && differencePoint4Y < DataCache.point4YDifferMax;
            Logs.WriteDebug("左右目point4Y差:" + (DataCache.point4Y_Differ ? "" : "异常:") + differencePoint4Y.ToString(), true);

            // Check Point20 X
            double Left_point20X = leftPoints["point20X"];
            double Right_point20X = rightPoints["point20X"];
            UpdateGridValues("Point20_X", Left_point20X, Right_point20X, DataCache.point20XDifferMin, DataCache.point20XDifferMax);
            double differencePoint20X = Left_point20X - Right_point20X;
            DataCache.point20X_Differ = differencePoint20X > DataCache.point20XDifferMin && differencePoint20X < DataCache.point20XDifferMax;
            Logs.WriteDebug("左右目point20X差:" + (DataCache.point20X_Differ ? "" : "异常:") + differencePoint20X.ToString(), true);

            // Check Point20 Y
            double Left_point20Y = leftPoints["point20Y"];
            double Right_point20Y = rightPoints["point20Y"];
            UpdateGridValues("Point20_Y", Left_point20Y, Right_point20Y, DataCache.point20YDifferMin, DataCache.point20YDifferMax);
            double differencePoint20Y = Left_point20Y - Right_point20Y;
            DataCache.point20Y_Differ = differencePoint20Y > DataCache.point20YDifferMin && differencePoint20Y < DataCache.point20YDifferMax;
            Logs.WriteDebug("左右目point20Y差:" + (DataCache.point20Y_Differ ? "" : "异常:") + differencePoint20Y.ToString(), true);

            // Check Point24 X
            double Left_point24X = leftPoints["point24X"];
            double Right_point24X = rightPoints["point24X"];
            UpdateGridValues("Point24_X", Left_point24X, Right_point24X, DataCache.point24XDifferMin, DataCache.point24XDifferMax);
            double differencePoint24X = Left_point24X - Right_point24X;
            DataCache.point24X_Differ = differencePoint24X > DataCache.point24XDifferMin && differencePoint24X < DataCache.point24XDifferMax;
            Logs.WriteDebug("左右目point24X差:" + (DataCache.point24X_Differ ? "" : "异常:") + differencePoint24X.ToString(), true);

            // Check Point24 Y
            double Left_point24Y = leftPoints["point24Y"];
            double Right_point24Y = rightPoints["point24Y"];
            UpdateGridValues("Point24_Y", Left_point24Y, Right_point24Y, DataCache.point24YDifferMin, DataCache.point24YDifferMax);
            double differencePoint24Y = Left_point24Y - Right_point24Y;
            DataCache.point24Y_Differ = differencePoint24Y > DataCache.point24YDifferMin && differencePoint24Y < DataCache.point24YDifferMax;
            Logs.WriteDebug("左右目point24Y差:" + (DataCache.point24Y_Differ ? "" : "异常:") + differencePoint24Y.ToString(), true);
        }

        private void CheckAngle2()
        {
            if (TB_LeftOledSn.Text =="" || TB_RightOledSn.Text=="")
            {
                return;
            }

            Logs.WriteDebug("CheckAngle2", true);
            double Leftangle;
            MySqlController.GetAngle2(TB_LeftOledSn.Text, out Leftangle);
            double Rightangle;
            MySqlController.GetAngle2(TB_RightOledSn.Text, out Rightangle);

            // Update Angle2 values in grid and check difference
            UpdateGridValues("angle", Leftangle, Rightangle, DataCache.angle2DifferMin, DataCache.angle2DifferMax);

            // Calculate difference and update status
            double difference = Leftangle - Rightangle;
            if (difference > DataCache.angle2DifferMin && difference < DataCache.angle2DifferMax)
            {
                Logs.WriteDebug("左右目angle2差:" + difference.ToString(), true);
                DataCache.angle2Differ = true;
            }
            else
            {
                Logs.WriteDebug("左右目angle2差异常:" + difference.ToString(), true);
                DataCache.angle2Differ = false;
            }
        }

        private void CheckDiopter()
        {
            return;
            //tb_diopterDiffer.BackColor = Color.White;
            //double LeftDiopter;
            //int status1 = MySqlController.GetDiopter(TB_LeftBracketSn.Text, out LeftDiopter);
            //double RightDiopter;
            //int status2 = MySqlController.GetDiopter(TB_RightBracketSn.Text, out RightDiopter);
            //if (status1 != 0 || status2 != 0)
            //{
            //    Logs.WriteDebug("未找到左右目diopter，跳过", true);
            //    DataCache.diopterDiffer = true;
            //    return;
            //}


            //double difference = LeftDiopter - RightDiopter;

            //tb_diopterL.Text = LeftDiopter.ToString("F4");
            //tb_diopterR.Text = RightDiopter.ToString("F4");
            //tb_diopterDiffer.Text = difference.ToString("F4");

            //if (difference >= DataCache.diopterDifferMin && difference <= DataCache.diopterDifferMax)
            //{
            //    // 差在指定范围内
            //    Logs.WriteDebug("左右目diopter差:" + difference.ToString(), true);
            //    tb_diopterDiffer.BackColor = Color.LightGreen;
            //    DataCache.diopterDiffer = true;
            //}
            //else
            //{
            //    // 差超出指定范围
            //    Logs.WriteDebug("左右目diopter差异常:" + difference.ToString(), true);
            //    tb_diopterDiffer.BackColor = Color.PaleVioletRed;
            //    DataCache.diopterDiffer = false;
            //}
        }

        private void sqlExcute()
        {
            string beamSn = ToHalfWidth(beam_sn_label.Text);
            string leftBracket = ToHalfWidth(TB_LeftBracketSn.Text);
            string leftOled = ToHalfWidth(TB_LeftOledSn.Text);
            string rightBracket = ToHalfWidth(TB_RightBracketSn.Text);
            string rightOled = ToHalfWidth(TB_RightOledSn.Text);
            Logs.WriteDebug("beamSn:" + beamSn, true);

            int ret = MySqlController.InsertDataOld(beamSn, LOGINFO.lname, DataCache.finalResult,leftBracket,leftOled,rightBracket,rightOled);
            if(ret != 0)
            {
                MessageBox.Show(this, "数据库写入失败");
            }
            CleanAll();
        }
        #endregion

        /*
        #region SN焦点事件
        private void TB_BeamSn_Enter(object sender, EventArgs e)
        {
            TB_BeamSn.BackColor = Color.Gold;
        }

        private void TB_BeamSn_Leave(object sender, EventArgs e)
        {
            TB_BeamSn.BackColor = Color.White;
        }
        */

        /// <summary>
        /// 将字符串中的全角字符（字母、数字、符号）转换为对应的半角字符。
        /// </summary>
        /// <param name="fullWidthStr">包含全角字符的字符串。</param>
        /// <returns>转换后的字符串。</returns>
        public static string ToHalfWidth(string fullWidthStr)
        {
            if (string.IsNullOrEmpty(fullWidthStr))
            {
                return fullWidthStr;
            }

            var sb = new System.Text.StringBuilder(fullWidthStr.Length);
            foreach (char c in fullWidthStr)
            {
                // 全角空格转半角空格
                if (c == '\u3000')
                {
                    sb.Append('\u0020');
                }
                // 全角字母、数字、符号转半角
                else if (c >= '\uFF01' && c <= '\uFF5E')
                {
                    sb.Append((char)(c - 65248));
                }
                else
                {
                    sb.Append(c); // 其他字符（如中文字符）不变
                }
            }
            return sb.ToString();
        }

        private void TB_LeftBracketSn_Enter(object sender, EventArgs e)
        {
            TB_LeftBracketSn.BackColor = Color.Gold;
        }

        private void TB_LeftBracketSn_Leave(object sender, EventArgs e)
        {
            TB_LeftBracketSn.BackColor = Color.White;
        }

        private void TB_LeftOledSn_Enter(object sender, EventArgs e)
        {
            TB_LeftOledSn.BackColor = Color.Gold;
        }

        private void TB_LeftOledSn_Leave(object sender, EventArgs e)
        {
            TB_LeftOledSn.BackColor = Color.White;
        }

        private void TB_RightBracketSn_Enter(object sender, EventArgs e)
        {
            TB_RightBracketSn.BackColor = Color.Gold;
        }

        private void TB_RightBracketSn_Leave(object sender, EventArgs e)
        {
            TB_RightBracketSn.BackColor = Color.White;
        }

        private void TB_RightOledSn_Enter(object sender, EventArgs e)
        {
            TB_RightOledSn.BackColor = Color.Gold;
        }

        private void TB_RightOledSn_Leave(object sender, EventArgs e)
        {
            TB_RightOledSn.BackColor = Color.White;
        }

        private void TB_FixSN_Enter(object sender, EventArgs e)
        {
            TB_FixSN.BackColor = Color.Gold;
        }

        private void TB_FixBeam_Enter(object sender, EventArgs e)
        {
            tb_FixBeam.BackColor = Color.Gold;
        }


        private void TB_FixBeam_Leave(object sender, EventArgs e)
        {
            tb_FixBeam.BackColor = Color.White;
        }


        private void TB_FixSN_Leave(object sender, EventArgs e)
        {
            TB_FixSN.BackColor = Color.White;
        }

        #region SN按键事件
        private void Btn_CleanLeft_Click(object sender, EventArgs e)
        {
            TB_LeftBracketSn.Text = "";
            TB_LeftOledSn.Text = "";
            CheckTestValue();
            CheckResult();
            CleanAll();
            TB_LeftBracketSn.Focus();
        }

        private void Btn_CleanRight_Click(object sender, EventArgs e)
        {
            TB_RightBracketSn.Text = "";
            TB_RightOledSn.Text = "";
            CheckTestValue();
            CheckResult();
            CleanAll();
            TB_RightBracketSn.Focus();
        }


        private void TB_FixSN_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Control || e.KeyCode == Keys.Enter)
            {
                //CleanAll();
                TB_LeftBracketSn.Text = "";
                TB_LeftOledSn.Text = "";
                TB_RightBracketSn.Text = "";
                TB_RightOledSn.Text = "";

                // Reset all grid cells
                foreach (DataGridViewRow row in dataGridView1.Rows)
                {
                    foreach (DataGridViewCell cell in row.Cells)
                    {
                        cell.Value = "";
                        cell.Style.BackColor = Color.White;
                    }
                }

                beam_sn_label.Text = tb_FixBeam.Text;
                Logs.WriteInfo("tb_FixBeam " + tb_FixBeam.Text, true);
                Label_Tip.Visible = false;
                string sn = TB_FixSN.Text;
                double mtf = 0;
                double gray = 0;
                string msg = "";
                string side = "";
                auto_oledsn = true;

                if (sn.Length != DataCache.bracketSnLength)
                {
                    msg = "套筒SN码长度不符！";
                }
                else if (MySqlController.GetTestValue(sn, out mtf, out gray) != 0)
                {
                    msg = "查询单目测试数据失败！";
                }

                if (radioButton3.Checked == true)
                    side = "L";
                else if (radioButton4.Checked == true)
                    side = "R";
                else
                {
                    MessageBox.Show("请先选择左或右套筒");
                    return;
                }

                if (msg == "")
                {
                    if (side == "L")
                    {
                        // Update grid with left values
                        dataGridView1.Rows[0].Cells["MTF"].Value = mtf.ToString("F4");
                        dataGridView1.Rows[0].Cells["gray"].Value = gray.ToString("F4");

                        Logs.WriteDebug("自动获取左目Oledsn", true);
                        string leftoled = "";
                        string beamsn = "";
                        int flag = MySqlController.GetLeftOledSnByBracketSn(sn, out leftoled, out beamsn);
                        TB_LeftOledSn.Text = leftoled;
                        TB_LeftBracketSn.Text = sn;
                        //beam_sn_label.Text = beamsn;
                        if (flag != 0)
                        {
                            Label_Tip.Text = "左屏SN码数据库中未找到！";
                            Label_Tip.Visible = true;
                            TB_LeftBracketSn.Text = "";
                            TB_LeftBracketSn.Focus();
                            TB_LeftBracketSn.SelectAll();
                        }
                        else
                        {
                            TB_LeftOledSn.BackColor = Color.LightGreen;
                            TB_LeftBracketSn.BackColor = Color.LightGreen;
                            TB_RightBracketSn.Focus();
                            TB_RightBracketSn.SelectAll();
                        }
                    }
                    else
                    {
                        // Update grid with right values
                        dataGridView1.Rows[1].Cells["MTF"].Value = mtf.ToString("F4");
                        dataGridView1.Rows[1].Cells["gray"].Value = gray.ToString("F4");

                        Logs.WriteDebug("自动获取右目Oledsn", true);
                        string rightoled = "";
                        string beamsn = "";
                        int flag = MySqlController.GetRightOledSnByBracketSn(sn, out rightoled, out beamsn);
                        TB_RightOledSn.Text = rightoled;
                        TB_RightBracketSn.Text = sn;
                        //beam_sn_label.Text = beamsn;

                        if (flag != 0)
                        {
                            Label_Tip.Text = "右屏SN码数据库中未找到！";
                            Label_Tip.Visible = true;
                            TB_RightBracketSn.Text = "";
                            TB_RightBracketSn.Focus();
                            TB_RightBracketSn.SelectAll();
                        }
                        else
                        {
                            TB_RightOledSn.BackColor = Color.LightGreen;
                            TB_RightBracketSn.BackColor = Color.LightGreen;
                            TB_LeftBracketSn.Focus();
                            TB_LeftBracketSn.SelectAll();
                        }
                    }

                    //radioButton3.Checked = false;
                    //radioButton4.Checked = false;
                }
            }
        }

        private void TB_FixBeam_KeyUp(object sender, KeyEventArgs e)
        {
            string msg = "";
            if (e.KeyCode == Keys.Control || e.KeyCode == Keys.Enter)
            {
                if (tb_FixBeam.Text.Length != DataCache.bracketSnLength)
                {
                    msg = "横梁SN码长度不符！";
                    Label_Tip.Text = msg;
                    Label_Tip.Visible = true;
                    return;
                }

                beam_sn_label.Text = ToHalfWidth(tb_FixBeam.Text);
                Logs.WriteDebug("tb_FixBeam:" + beam_sn_label.Text, true);
                TB_FixSN.Focus();
                Label_Tip.Text = "";
                Label_Tip.Visible = false;
            }
        }

        private void TB_LeftBracketSn_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Control || e.KeyCode == Keys.Enter)
            {
                Label_Tip.Visible = false;
                string sn = TB_LeftBracketSn.Text;
                double mtf = 0;
                double gray = 0;
                string msg = "";

                if (sn.Length != DataCache.bracketSnLength)
                {
                    msg = "套筒SN码长度不符！";
                }
                else if (MySqlController.GetTestValue(sn, out mtf, out gray) != 0)
                {
                    msg = "查询单目测试数据失败！";
                }

                if (msg == "")
                {
                    // Update grid with left values
                    dataGridView1.Rows[0].Cells["MTF"].Value = mtf.ToString("F4");
                    dataGridView1.Rows[0].Cells["gray"].Value = gray.ToString("F4");

                    TB_LeftOledSn.Focus();
                    TB_LeftOledSn.SelectAll();
                }
                else
                {
                    // Reset left values in grid
                    dataGridView1.Rows[0].Cells["MTF"].Value = "";
                    dataGridView1.Rows[0].Cells["gray"].Value = "";

                    Label_Tip.Text = msg;
                    Label_Tip.Visible = true;
                    TB_LeftBracketSn.Text = "";
                    TB_LeftBracketSn.Focus();
                }

                CheckTestValue();
                CheckResult();
            }
        }

        private void TB_LeftOledSn_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Control || e.KeyCode == Keys.Enter)
            {
                Label_Tip.Visible = false;
                string sn = TB_LeftOledSn.Text;
                if (sn.Length != DataCache.oledSnLength)
                {
                    Label_Tip.Text = "组件一SN码长度不符！";
                    Label_Tip.Visible = true;
                    TB_LeftOledSn.Text = "";
                    TB_LeftOledSn.Focus();
                }
                else
                {
                    TB_RightBracketSn.Focus();
                    TB_RightBracketSn.SelectAll();
                }

                CheckResult();
            }
        }

        private void TB_RightBracketSn_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Control || e.KeyCode == Keys.Enter)
            {
                Label_Tip.Visible = false;
                string sn = TB_RightBracketSn.Text;
                double mtf = 0;
                double gray = 0;
                string msg = "";

                if (sn.Length != DataCache.bracketSnLength)
                {
                    msg = "套筒SN码长度不符！";
                }
                else if (MySqlController.GetTestValue(sn, out mtf, out gray) != 0)
                {
                    msg = "查询单目测试数据失败！";
                }

                if (msg == "")
                {
                    // Update grid with right values
                    dataGridView1.Rows[1].Cells["MTF"].Value = mtf.ToString("F4");
                    dataGridView1.Rows[1].Cells["gray"].Value = gray.ToString("F4");

                    TB_RightOledSn.Focus();
                    TB_RightOledSn.SelectAll();
                }
                else
                {
                    // Reset right values in grid
                    dataGridView1.Rows[1].Cells["MTF"].Value = "";
                    dataGridView1.Rows[1].Cells["gray"].Value = "";

                    Label_Tip.Text = msg;
                    Label_Tip.Visible = true;
                    TB_RightBracketSn.Text = "";
                    TB_RightBracketSn.Focus();
                }

                CheckTestValue();
                CheckResult();
            }
        }

        private void TB_RightOledSn_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Control || e.KeyCode == Keys.Enter)
            {
                Label_Tip.Visible = false;
                string sn = TB_RightOledSn.Text;
                if (sn.Length != DataCache.oledSnLength)
                {
                    Label_Tip.Text = "组件一SN码长度不符！";
                    Label_Tip.Visible = true;
                    TB_RightOledSn.Text = "";
                    TB_RightOledSn.Focus();
                }
                else
                {
                    Group_Sn.Focus();
                }

                CheckResult();              
            }
        }
        #endregion

        #region 不良项
        private DataTable optCode;
        private void Position_IndexChange(object sender, EventArgs e)
        {
            ComboBox cb = (ComboBox)sender;
            string name = cb.Name;
            string seq = name.Split('_')[2];
            if (cb.Text != "无")
            {
                DataTable dt = optCode.Select("位置 = '" + cb.Text + "'").CopyToDataTable();
                ComboBox temp = (ComboBox)Controls.Find("CB_Reject_" + seq, true)[0];
                temp.DataSource = dt;
                temp.DisplayMember = "不良项";
                temp.ValueMember = "不良代码";
            }
            else
            {
                ((ComboBox)Controls.Find("CB_Reject_" + seq, true)[0]).DataSource = null;
            }

        }

        private void Reject_IndexChange(object sender, EventArgs e)
        {
            ComboBox cb = (ComboBox)sender;
            string name = cb.Name;
            string seq = name.Split('_')[2];
            ((TextBox)Controls.Find("TB_Code_" + seq, true)[0]).Text = (string)cb.SelectedValue;

            CheckResult();
        }

        /// <summary>
        /// 将CSV文件的数据读取到DataTable中
        /// </summary>
        /// <param name="fileName">CSV文件路径</param>
        /// <returns>返回读取了CSV数据的DataTable</returns>
        public static DataTable OpenCSV(string filePath)
        {
            Encoding encoding = Encoding.Default;//GetEncoding(filePath); //Encoding.ASCII;//
            DataTable dt = new DataTable();
            FileStream fs = new FileStream(filePath, System.IO.FileMode.Open, System.IO.FileAccess.Read);

            //StreamReader sr = new StreamReader(fs, Encoding.UTF8);
            StreamReader sr = new StreamReader(fs, encoding);
            //string fileContent = sr.ReadToEnd();
            //encoding = sr.CurrentEncoding;
            //记录每次读取的一行记录
            string strLine = "";
            //记录每行记录中的各字段内容
            string[] aryLine = null;
            string[] tableHead = null;
            //标示列数
            int columnCount = 0;
            //标示是否是读取的第一行
            bool IsFirst = true;
            //逐行读取CSV中的数据
            while ((strLine = sr.ReadLine()) != null)
            {
                //strLine = Common.ConvertStringUTF8(strLine, encoding);
                //strLine = Common.ConvertStringUTF8(strLine);

                if (IsFirst == true)
                {
                    tableHead = strLine.Split(',');
                    IsFirst = false;
                    columnCount = tableHead.Length;
                    //创建列
                    for (int i = 0; i < columnCount; i++)
                    {
                        DataColumn dc = new DataColumn(tableHead[i]);
                        dt.Columns.Add(dc);
                    }
                }
                else
                {
                    aryLine = strLine.Split(',');
                    DataRow dr = dt.NewRow();
                    for (int j = 0; j < columnCount; j++)
                    {
                        dr[j] = aryLine[j];
                    }
                    dt.Rows.Add(dr);
                }
            }
            if (aryLine != null && aryLine.Length > 0)
            {
                dt.DefaultView.Sort = tableHead[0] + " " + "asc";
            }

            sr.Close();
            fs.Close();
            return dt;
        }
        #endregion

        #region 最终确认
        private void Btn_OK_Click(object sender, EventArgs e)
        {
            sqlExcute();
        }

        private void Btn_NG_Click(object sender, EventArgs e)
        {
            sqlExcute();
            XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
        }

        #endregion

        private void TB_Remark_TextChanged(object sender, EventArgs e)
        {

        }

        private void groupBox1_Enter(object sender, EventArgs e)
        {

        }

        private void groupBox2_Enter(object sender, EventArgs e)
        {

        }

        private void radioButton1_CheckedChanged(object sender, EventArgs e)
        {
            if (this.radioButton1.Checked)
            {
                this.radioButton2.Checked = false;
                this.TB_FixSN.Visible = false;
                this.groupBox1.Visible = false;
                //this.TB_BeamSn.Focus();
                //this.TB_BeamSn.BackColor = Color.Gold;
                CleanAll();
                TB_FixSN.Text = "";
                TB_LeftBracketSn.BackColor = Color.White;
                TB_RightBracketSn.BackColor = Color.White;
                TB_LeftOledSn.BackColor = Color.White;
                TB_RightOledSn.BackColor = Color.White;

                beam_sn_label.Text = "";
                Logs.WriteInfo("radioButton1_CheckedChanged", true);
                XrPLCom.TestAbortUpload(resultBuf, ref resultLen);
            }
        }

        private void radioButton2_CheckedChanged(object sender, EventArgs e)
        {
            if (this.radioButton2.Checked)
            {
                this.radioButton1.Checked = false;
                this.TB_FixSN.Visible = true;
                this.groupBox1.Visible = true;
                this.tb_FixBeam.Visible = true;
                this.radioButton3.Checked = true;
                this.tb_FixBeam.Focus();
                //this.TB_FixSN.Focus();
                this.tb_FixBeam.BackColor = Color.Gold;
                CleanAll();
                tb_FixBeam.Text = "";
                TB_FixSN.Text = "";
                TB_LeftBracketSn.BackColor = Color.White;
                TB_RightBracketSn.BackColor = Color.White;
                TB_LeftOledSn.BackColor = Color.White;
                TB_RightOledSn.BackColor = Color.White;
            }
        }

        private void radioButton3_CheckedChanged(object sender, EventArgs e)
        {
            TB_FixSN.Text = "";
            tb_FixBeam.Text = "";
            beam_sn_label.Text = "";

            CleanAll();
            tb_FixBeam.Focus();


            //TB_FixSN.Focus();
        }

        private void radioButton4_CheckedChanged(object sender, EventArgs e)
        {
            TB_FixSN.Text = "";
            tb_FixBeam.Text = "";
            beam_sn_label.Text = "";

            CleanAll();
            tb_FixBeam.Focus();


            //TB_FixSN.Focus();
        }
    }
}
